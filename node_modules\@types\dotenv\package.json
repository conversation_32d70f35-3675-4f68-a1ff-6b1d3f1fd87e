{"name": "@types/dotenv", "version": "6.1.1", "description": "TypeScript definitions for dotenv", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/jussikinnula", "githubUsername": "j<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/borekb", "githubUsername": "borekb"}, {"name": "<PERSON>", "url": "https://github.com/enaeseth", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/maxbeatty", "githubUsername": "maxbeatty"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/dotenv"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "af1dbc31d3cee4a71db93ee1935cace95f127faa78918457eb1660577bb04098", "typeScriptVersion": "2.0"}