{"name": "@types/helmet", "version": "0.0.48", "description": "TypeScript definitions for helmet", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/EvanHahn", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/bluehatbrit", "githubUsername": "bluehatbrit"}, {"name": "<PERSON>", "url": "https://github.com/ch<PERSON><PERSON>mueller", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/helmet"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "1e80e0540122bcd46ceaf307b5d864740296c71d67cf94fccbfa21b523f41873", "typeScriptVersion": "3.1"}