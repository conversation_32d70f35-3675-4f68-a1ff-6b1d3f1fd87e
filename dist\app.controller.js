"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = require("dotenv");
const path_1 = __importDefault(require("path"));
(0, dotenv_1.config)({ path: path_1.default.resolve("./config/.env") });
const app = (0, express_1.default)();
const port = process.env.PORT || 5000;
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    limit: 10,
    message: {
        error: "Too many requests from this IP, please try again after 15 minutes"
    },
    statusCode: 429,
    legacyHeaders: false,
});
const bootstarp = () => {
    app.use(express_1.default.json());
    app.use((0, cors_1.default)());
    app.use((0, helmet_1.default)());
    app.use(limiter);
    app.get("/*demo", (req, res) => {
        return res.status(404).json({ message: `invalid url ${req.originalUrl}` });
    });
    app.get("/", (req, res) => {
        return res.status(200).json({ message: "welcome with my soccial app" });
    });
    app.listen(port, () => {
        console.log(`Server is running at http://localhost:${port}`);
    });
};
exports.default = bootstarp;
