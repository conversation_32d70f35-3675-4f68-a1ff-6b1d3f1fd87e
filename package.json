{"name": "socialapp", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"start:dev": "concurrently \"tsc --watch\" \"nodemon dist/index.js\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"concurrently": "^9.2.1", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "helmet": "^8.1.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/dotenv": "^6.1.1", "@types/express": "^5.0.3", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48"}}